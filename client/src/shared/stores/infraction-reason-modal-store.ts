import { create } from 'zustand'

interface InfractionReasonModalStore {
	open: boolean
	infractionId: string | null
	onOpen: (infractionId: string) => void
	onClose: () => void
}

export const useInfractionReasonModalStore = create<InfractionReasonModalStore>(set => ({
	open: false,
	infractionId: null,
	onOpen: (infractionId: string) => set({ open: true, infractionId }),
	onClose: () => set({ open: false, infractionId: null })
}))
