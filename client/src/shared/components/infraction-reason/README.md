# Infraction Reason Component

This component provides functionality for adding reasons to infractions in the personal profile page.

## Features

- **Modal-based form**: Clean modal interface for adding infraction reasons
- **File upload support**: Optional file attachment with infraction reasons
- **Zustand state management**: Clean state management for modal open/close
- **Form validation**: Required fields with minimum length validation
- **API integration**: Seamless integration with backend infraction-reason endpoints

## Components

### InfractionReasonModal
Main modal component that handles the form submission and API calls.

### InfractionReasonForm
Form component with validation for name, description, and file upload.

### InfractionsTab
Tab component for the profile page that displays user's infractions with "Add Reason" buttons.

## Usage

The component is automatically included in the personal profile page under the "Qoidabuzarliklar" tab. Users can:

1. View their infractions
2. Click "Sabab qo'shish" button to open the modal
3. Fill in the reason name and description
4. Optionally upload a file
5. Submit to add the reason to the infraction

## API Endpoints

- `POST /infraction-reason` - Create new infraction reason
- `GET /infraction-reason/:id` - Get specific infraction reason

## State Management

Uses Zustand store (`useInfractionReasonModalStore`) for managing modal state:
- `open`: Boolean for modal visibility
- `infractionId`: Current infraction ID for adding reason
- `onOpen(id)`: Open modal with specific infraction ID
- `onClose()`: Close modal and reset state
