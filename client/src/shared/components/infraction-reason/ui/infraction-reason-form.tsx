import { Form, Input } from 'antd'
import { useFileUpload } from '@/shared/components/file-upload/file-upload'
import { useEffect } from 'react'

const { TextArea } = Input

interface InfractionReasonFormProps {
	loading?: boolean
	onFileIdsChange?: (fileIds: string[]) => void
}

export const InfractionReasonForm = ({ loading, onFileIdsChange }: InfractionReasonFormProps) => {
	const { fileIds, render: FileUpload } = useFileUpload()

	// Notify parent component when file IDs change
	useEffect(() => {
		if (onFileIdsChange) {
			onFileIdsChange(fileIds)
		}
	}, [fileIds, onFileIdsChange])

	return (
		<>
			<Form.Item
				label="Sabab nomi"
				name="name"
				rules={[
					{ required: true, message: 'Sabab nomini kiriting!' },
					
				]}
			>
				<Input placeholder="Sabab nomini kiriting" disabled={loading} />
			</Form.Item>

			<Form.Item
				label="Tavsif"
				name="description"
				rules={[
					{ required: true, message: '<PERSON>v<PERSON><PERSON><PERSON> kiriting!' },
				]}
			>
				<TextArea
					rows={4}
					placeholder="Qoidabuzarlik sababini batafsil yozing"
					disabled={loading}
				/>
			</Form.Item>

			<FileUpload />
		</>
	)
}
