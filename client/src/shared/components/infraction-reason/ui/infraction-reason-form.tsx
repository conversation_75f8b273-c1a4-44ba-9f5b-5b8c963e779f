import { Form, Input, Upload, Button, message } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import { useState, useEffect } from 'react'
import { axiosPrivate } from '@/config/api'
import { fileEndpoints } from '@/config/api/endpoints'

const { TextArea } = Input

interface InfractionReasonFormProps {
	loading?: boolean
}

export const InfractionReasonForm = ({ loading }: InfractionReasonFormProps) => {
	const [fileList, setFileList] = useState<any[]>([])
	const [uploadedFiles, setUploadedFiles] = useState<string[]>([])
	const form = Form.useFormInstance()

	useEffect(() => {
		if (form && uploadedFiles.length > 0) {
			form.setFieldValue('files', uploadedFiles)
		}
	}, [uploadedFiles, form])

	const handleUpload = async (file: File) => {
		const formData = new FormData()
		formData.append('file', file)

		try {
			const response = await axiosPrivate.post(fileEndpoints.upload, formData, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			})

			if (response.data?.id) {
				setUploadedFiles(prev => [...prev, response.data.id])
				message.success('Fayl muvaffaqiyatli yuklandi')
				return true
			}
		} catch (error) {
			message.error('Fayl yuklashda xatolik yuz berdi')
			return false
		}
	}

	const uploadProps = {
		beforeUpload: (file: File) => {
			handleUpload(file)
			return false // Prevent default upload
		},
		fileList,
		onChange: ({ fileList: newFileList }: any) => {
			setFileList(newFileList)
		},
		onRemove: (file: any) => {
			const index = fileList.indexOf(file)
			if (index > -1) {
				setUploadedFiles(prev => prev.filter((_, i) => i !== index))
			}
		}
	}

	return (
		<>
			<Form.Item
				name="files"
				hidden
			/>

			<Form.Item
				label="Sabab nomi"
				name="name"
				rules={[
					{ required: true, message: 'Sabab nomini kiriting!' },
					{ min: 3, message: 'Sabab nomi kamida 3 ta belgidan iborat bo\'lishi kerak!' }
				]}
			>
				<Input placeholder="Sabab nomini kiriting" disabled={loading} />
			</Form.Item>

			<Form.Item
				label="Tavsif"
				name="description"
				rules={[
					{ required: true, message: 'Tavsifni kiriting!' },
					{ min: 10, message: 'Tavsif kamida 10 ta belgidan iborat bo\'lishi kerak!' }
				]}
			>
				<TextArea
					rows={4}
					placeholder="Qoidabuzarlik sababini batafsil yozing"
					disabled={loading}
				/>
			</Form.Item>

			<Form.Item label="Fayl yuklash (ixtiyoriy)">
				<Upload {...uploadProps} disabled={loading}>
					<Button icon={<UploadOutlined />} disabled={loading}>
						Fayl tanlash
					</Button>
				</Upload>
			</Form.Item>
		</>
	)
}
