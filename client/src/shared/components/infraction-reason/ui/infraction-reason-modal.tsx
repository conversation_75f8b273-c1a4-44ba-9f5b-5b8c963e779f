import { Form, Modal, message } from 'antd'
import { useInfractionReasonModalStore } from '@/shared/stores/infraction-reason-modal-store'
import { InfractionReasonForm } from './infraction-reason-form'
import { useCreateInfractionReason } from '@/config/queries/infraction-reason/infraction-reason.queries'

export const InfractionReasonModal = () => {
	const [form] = Form.useForm()
	const { open, infractionId, onClose } = useInfractionReasonModalStore()
	const { mutateAsync: createInfractionReason, isPending } = useCreateInfractionReason()

	const handleClose = () => {
		form.resetFields()
		onClose()
	}

	const handleFinish = async (values: any) => {
		if (!infractionId) {
			message.error('Qoidabuzarlik ID topilmadi')
			return
		}

		try {
			await createInfractionReason({
				name: values.name,
				description: values.description,
				infractionId,
				files: values.files && values.files.length > 0 ? values.files : undefined
			})
			message.success('Qoidabuzarlik sababi muvaffaqiyatli qo\'shildi')
			handleClose()
		} catch (error) {
			message.error('Qoidabuzarlik sababini qo\'shishda xatolik yuz berdi')
		}
	}

	return (
		<Modal
			title="Qoidabuzarlik sababini qo'shish"
			open={open}
			onCancel={handleClose}
			onOk={() => form.submit()}
			okText="Qo'shish"
			cancelText="Bekor qilish"
			confirmLoading={isPending}
			destroyOnClose
			width={600}
		>
			<Form form={form} onFinish={handleFinish}>
				<InfractionReasonForm
					loading={isPending}
				/>
			</Form>
		</Modal>
	)
}
