import { Form, Modal, message } from 'antd'
import { useState } from 'react'
import { useInfractionReasonModalStore } from '../store/infraction-reason-modal-store'
import { InfractionReasonForm } from './infraction-reason-form'
import { useCreateInfractionReason } from '@/config/queries/infraction-reason/infraction-reason.queries'

export const InfractionReasonModal = () => {
	const [form] = Form.useForm()
	const [fileIds, setFileIds] = useState<string[]>([])
	const { open, infractionId, onClose } = useInfractionReasonModalStore()
	const { mutateAsync: createInfractionReason, isPending } = useCreateInfractionReason()

	const handleClose = () => {
		form.resetFields()
		setFileIds([])
		onClose()
	}

	const handleFinish = async (values: any) => {
		if (!infractionId) {
			message.error('Qoidabuzarlik ID topilmadi')
			return
		}

		try {
			await createInfractionReason({
				name: values.name,
				description: values.description,
				infractionId,
				files: fileIds.length > 0 ? fileIds : undefined
			})
			message.success('Qoidabuzarlik sababi muvaffaqiyatli qo\'shildi')
			handleClose()
		} catch (error) {
			message.error('Qoidabuzarlik sababini qo\'shishda xatolik yuz berdi')
		}
	}

	return (
		<Modal
			title={
				<div className="text-lg font-semibold dark:text-white">
					Qoidabuzarlik sababini qo'shish
				</div>
			}
			open={open}
			onCancel={handleClose}
			onOk={() => form.submit()}
			okText="Qo'shish"
			cancelText="Bekor qilish"
			confirmLoading={isPending}
			destroyOnClose
			width={700}
			centered
			styles={{
				body: {
					padding: '24px',
				}
			}}
		>
			<div className="dark:bg-gray-900 p-6 rounded-lg shadow-sm">
				<Form
					form={form}
					onFinish={handleFinish}
					layout="vertical"
					requiredMark={false}
				>
					<InfractionReasonForm
						loading={isPending}
						onFileIdsChange={setFileIds}
					/>
				</Form>
			</div>
		</Modal>
	)
}
