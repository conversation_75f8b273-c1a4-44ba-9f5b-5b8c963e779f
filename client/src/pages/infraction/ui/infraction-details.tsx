import { Card, Descriptions, Avatar, Typography, Spin, Empty, Button } from 'antd'
import { useParams, useNavigate, useLocation } from 'react-router-dom'
import { ArrowLeft, Plus } from 'lucide-react'
import dayjs from 'dayjs'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import { useGetInfractionById } from '@/config/queries/infraction/get-all.queries'
import { useInfractionReasonModalStore } from '@/pages/infraction/store/infraction-reason-modal-store'
import { InfractionReasonModal } from './infraction-reason-modal'

const { Title, Text } = Typography

const InfractionDetails = () => {
	const { id } = useParams<{ id: string }>()
	const navigate = useNavigate()
	const location = useLocation()
	const { onOpen } = useInfractionReasonModalStore()

	const { data: infractionData, isLoading, error } = useGetInfractionById(id!)

	// Check if we're in personal workspace
	const isPersonalWorkspace = location.pathname.includes('/personal')

	// Debug logging
	console.log('Infraction Data:', infractionData)
	console.log('InfractionReason:', infractionData?.data?.InfractionReason)

	if (isLoading) {
		return (
			<div className="p-6">
				<Card>
					<div className="flex justify-center items-center py-8">
						<Spin size="large" />
					</div>
				</Card>
			</div>
		)
	}

	if (error || !infractionData?.data) {
		return (
			<div className="p-6">
				<Card>
					<Empty description="Qoidabuzarlik ma'lumotlari topilmadi" />
				</Card>
			</div>
		)
	}

	const infraction = infractionData.data

	// Debug the InfractionReason rendering
	console.log('Checking InfractionReason:', infraction.InfractionReason, 'Length:', infraction.InfractionReason?.length)

	return (
		<div className="p-6">
			<Card>
				<div className="mb-4 flex items-center justify-between">
					<div className="flex items-center gap-3">
						<ArrowLeft
							className="cursor-pointer hover:text-blue-500"
							onClick={() => navigate(-1)}
							size={20}
						/>
						<Title level={3} className="mb-0">
							Qoidabuzarlik tafsilotlari
						</Title>
					</div>
					{isPersonalWorkspace && (
						<Button
							type="primary"
							icon={<Plus size={16} />}
							onClick={() => onOpen(id!)}
						>
							Sabab qo'shish
						</Button>
					)}
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{/* User Information */}
					<Card title="Foydalanuvchi ma'lumotlari" size="small">
						<div className="flex items-center gap-3 mb-4">
							<Avatar
								size={64}
								src={infraction.user?.avatar?.path}
							>
								{infraction.user?.fullName?.charAt(0)}
							</Avatar>
							<div>
								<Title level={4} className="mb-1">
									{infraction.user?.fullName}
								</Title>
								<Text type="secondary">{infraction.user?.phone}</Text>
							</div>
						</div>

						<Descriptions column={1} size="small">
							<Descriptions.Item label="Tashkilot">
								{infraction.user?.Organization?.[0]?.name || '-'}
							</Descriptions.Item>
							<Descriptions.Item label="Lavozim">
								{infraction.user?.position?.name || '-'}
							</Descriptions.Item>
						</Descriptions>
					</Card>

					{/* Infraction Information */}
					<Card title="Qoidabuzarlik ma'lumotlari" size="small">
						<Descriptions column={1} size="small">
							<Descriptions.Item label="Nomi">
								{infraction.name || '-'}
							</Descriptions.Item>
							<Descriptions.Item label="Tavsif">
								{infraction.description || '-'}
							</Descriptions.Item>
							<Descriptions.Item label="Qoidabuzarlik sanasi">
								{dayjs(infraction.infractionDate).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')}
							</Descriptions.Item>
							
						</Descriptions>
					</Card>
				</div>

				{/* Infraction Reasons */}
				{infraction.InfractionReason && infraction.InfractionReason.length > 0 && (
					<Card title="Qoidabuzarlik sabablari" className="mt-6" size="small">
						<div className="space-y-3">
							{infraction.InfractionReason.map((reason, index) => (
								<Card key={reason.id} size="small" className="border border-gray-200">
									<div className="space-y-3">
										<div className="flex justify-between items-start">
											<div className="flex-1">
												<Text strong className="text-base">Sabab #{index + 1}</Text>
												{reason.name && (
													<div className="mt-2">
														<Text strong className="text-gray-800">Nomi: </Text>
														<Text>{reason.name}</Text>
													</div>
												)}
												{reason.description && (
													<div className="mt-2">
														<Text strong className="text-gray-800">Tavsif: </Text>
														<Text>{reason.description}</Text>
													</div>
												)}
												<div className="mt-2">
													<Text type="secondary" className="text-xs">
														{dayjs(reason.createdAt).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')}
													</Text>
												</div>
											</div>
										</div>

										{/* File Display */}
										{reason.file && (
											<div className="border-t pt-3">
												<div className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
													<div className="flex items-center space-x-3">
														<div className="flex-shrink-0">
															<svg className="w-8 h-8 text-red-500" fill="currentColor" viewBox="0 0 20 20">
																<path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
															</svg>
														</div>
														<div>
															<div className="text-sm font-medium text-gray-900">
																Biriktirilgan fayl
															</div>
															<div className="text-xs text-gray-500">
																PDF hujjat
															</div>
														</div>
													</div>
													<a
														href={`${import.meta.env.VITE_BASE_URL}/${reason.file.path}`}
														target="_blank"
														rel="noopener noreferrer"
														className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
													>
														<svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
														</svg>
														Yuklab olish
													</a>
												</div>
											</div>
										)}
									</div>
								</Card>
							))}
						</div>
					</Card>
				)}
			</Card>
			<InfractionReasonModal />
		</div>
	)
}

export default InfractionDetails