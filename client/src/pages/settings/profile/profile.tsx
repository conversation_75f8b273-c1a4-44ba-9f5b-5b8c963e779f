import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { Card, Descriptions, Avatar, Tabs } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import { Link } from 'react-router-dom'
import { Organizations } from '../users/tabs/organizations'
import ScheduleTable from '../employee/ui/schedule-table'
import { Attendance } from '../users/tabs/attendance'
import { Vocation } from '../users/tabs/vocation'
import { useGetUserOne } from '@/config/queries/users/get-all.queries'
import { getImageUrl } from '@/shared/utils/getImageUrl'
import { InfractionsTab } from './ui/infractions-tab'

const locations = {
	IN: 'Hududda',
	OUT: 'Hududda emas',
	OFFLINE: 'Offline'
}

const Profile = () => {
	const { data: me } = useAuthMe()
	const { data: userData } = useGetUserOne(me?.id ?? '')

	return (
		<div style={{ padding: '24px' }}>
			<Card title="Foydalanuvchi ma'lumotlari">
				<div style={{ display: 'flex', gap: '24px', marginBottom: '24px' }}>
					<Avatar
						size={200}
						icon={<UserOutlined />}
						src={getImageUrl(me?.avatar?.path ?? '')}
					/>
					<div>
						<h2>{me?.fullName}</h2>
						<p>@{me?.username}</p>
					</div>
				</div>
				<div className='w-full flex justify-between gap-x-5'>
					<div className='w-[32%]'>
						<Descriptions
							bordered
							column={1}>
							<Descriptions.Item label='FIO'>{me?.fullName}</Descriptions.Item>
							<Descriptions.Item label='Telefon raqami'>{me?.username}</Descriptions.Item>
							<Descriptions.Item label="Qo'shimcha telefon raqami">{me?.phone}</Descriptions.Item>
							<Descriptions.Item label='Asosiy tashkiloti'>
								<Link to={`/workspace/organizations/${me?.MainOrganization?.id}`}>
									{me?.MainOrganization?.name}
								</Link>
							</Descriptions.Item>
							<Descriptions.Item label="Ma'sul">
								{Array.isArray(me?.ResponsibleFor) ? me.ResponsibleFor[0]?.name : ''}
							</Descriptions.Item>
							<Descriptions.Item label='Lavozim'>
								{(userData?.position as { name: string })?.name}
							</Descriptions.Item>
							<Descriptions.Item label='IMEI'>{userData?.imei}</Descriptions.Item>
							<Descriptions.Item label='Ta`tildami'>
								{userData?.isinVocation ? 'Ha' : 'Yo`q'}
							</Descriptions.Item>
							{!!userData?.completedTaskCount && !!userData.inCompletedTaskCount ? (
								<Descriptions.Item label='Topshiriqlar statistikasi'>
									{((userData.completedTaskCount + userData.inCompletedTaskCount) *
										userData.completedTaskCount) /
										100}
									%
								</Descriptions.Item>
							) : (
								<Descriptions.Item label='Topshiriqlar statistikasi'>
									Topshiriqlar yo`q
								</Descriptions.Item>
							)}
							<Descriptions.Item label='Loqatsiyasi'>
								{locations[userData?.gpsStatus as keyof typeof locations]}
							</Descriptions.Item>
						</Descriptions>
					</div>
					<div className='flex-auto'>
						<Tabs
							type='card'
							items={[
								{
									label: 'Tashkilotlar',
									key: '3',
									children: (
										<div>
											<div className='flex items-center justify-between'>
												<h1>Tashkilotlar</h1>
											</div>

											<Organizations organizations={userData?.Organization ?? []} />
										</div>
									)
								},
								{
									label: 'Ish jadvali',
									key: '2',
									children: (
										<div>
											<div className='flex items-center justify-between'>
												<h1 className='font-bold'>Ish jadvali</h1>
											</div>
											<ScheduleTable workspace='personal' />
										</div>
									)
								},
								{ label: 'Davomat', key: '1', children: <Attendance workspace='personal' /> },
								{ label: 'Ta`til tarixi', key: '4', children: <Vocation workspace='personal' /> },
								{ label: 'Qoidabuzarliklar', key: '5', children: <InfractionsTab /> }
							]}
						/>
					</div>
				</div>
			</Card>
		</div>
	)
}
export default Profile
