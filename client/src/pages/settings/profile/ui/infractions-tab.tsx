import { Card, Button, Empty, Tag, Typography, Space, Spin } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { useGetInfractions } from '@/config/queries/infraction/get-all.queries'
import { useInfractionReasonModalStore } from '@/shared/stores/infraction-reason-modal-store'
import { InfractionReasonModal } from '@/shared/components/infraction-reason'
import dayjs from 'dayjs'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'

const { Text, Title } = Typography

export const InfractionsTab = () => {
	const { data: infractions, isLoading } = useGetInfractions('personal')
	const { onOpen } = useInfractionReasonModalStore()

	if (isLoading) {
		return (
			<div className="flex justify-center items-center py-8">
				<Spin size="large" />
			</div>
		)
	}

	const infractionsList = Array.isArray(infractions) ? infractions : []

	if (infractionsList.length === 0) {
		return (
			<>
				<Empty description="Qoidabuzarliklar topilmadi" />
				<InfractionReasonModal />
			</>
		)
	}

	return (
		<>
			<div className="space-y-4">
				{infractionsList.map((infraction) => (
					<Card
						key={infraction.id}
						size="small"
						className="border border-gray-200"
						title={
							<div className="flex justify-between items-center">
								<div>
									<Title level={5} className="mb-1">
										{infraction.name || 'Qoidabuzarlik'}
									</Title>
									<Text type="secondary" className="text-sm">
										{dayjs(infraction.infractionDate).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')}
									</Text>
								</div>
								<Button
									type="primary"
									size="small"
									icon={<PlusOutlined />}
									onClick={() => onOpen(infraction.id)}
								>
									Sabab qo'shish
								</Button>
							</div>
						}
					>
						{infraction.description && (
							<div className="mb-3">
								<Text>{infraction.description}</Text>
							</div>
						)}

						{/* Existing Infraction Reasons */}
						{infraction.InfractionReason && infraction.InfractionReason.length > 0 && (
							<div className="mt-4">
								<Title level={5} className="mb-2">
									Qo'shilgan sabablar:
								</Title>
								<div className="space-y-2">
									{infraction.InfractionReason.map((reason, index) => (
										<Card key={reason.id} size="small" className="bg-gray-50">
											<div className="flex justify-between items-start">
												<div className="flex-1">
													<Space direction="vertical" size="small" className="w-full">
														<Text strong>Sabab #{index + 1}</Text>
														{reason.name && (
															<div>
																<Text className="text-gray-600">Nomi: </Text>
																<Text>{reason.name}</Text>
															</div>
														)}
														{reason.description && (
															<div>
																<Text className="text-gray-600">Tavsif: </Text>
																<Text>{reason.description}</Text>
															</div>
														)}
														<Text type="secondary" className="text-xs">
															{dayjs(reason.createdAt).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')}
														</Text>
													</Space>
												</div>
												{reason.file && (
													<Tag color="blue">Fayl mavjud</Tag>
												)}
											</div>
										</Card>
									))}
								</div>
							</div>
						)}

						{(!infraction.InfractionReason || infraction.InfractionReason.length === 0) && (
							<div className="text-center py-4">
								<Text type="secondary">Hali sabablar qo'shilmagan</Text>
							</div>
						)}
					</Card>
				))}
			</div>
			<InfractionReasonModal />
		</>
	)
}
